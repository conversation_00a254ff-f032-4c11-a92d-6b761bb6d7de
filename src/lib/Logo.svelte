<script lang="ts">
	interface Props {
		class?: string;
	}

	let { class: className = '' }: Props = $props();
</script>

<svg
	class="h-16 w-16 mx-auto {className}"
	viewBox="0 0 64 64"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	aria-label="Hitez Z Logo"
>
	<!-- Minimalist Z logo design -->
	<g class="logo-group">
		<!-- Top horizontal line -->
		<path
			class="logo-line logo-top"
			d="M12 16 L52 16 L52 20 L12 20 Z"
			fill="currentColor"
		/>

		<!-- Diagonal line -->
		<path
			class="logo-line logo-diagonal"
			d="M48 20 L20 44 L16 44 L44 20 Z"
			fill="currentColor"
		/>

		<!-- Bottom horizontal line -->
		<path
			class="logo-line logo-bottom"
			d="M12 44 L52 44 L52 48 L12 48 Z"
			fill="currentColor"
		/>
	</g>
</svg>

<style>
	.logo-line {
		transform-origin: center;
	}
</style>
