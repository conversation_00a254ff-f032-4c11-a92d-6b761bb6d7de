<script lang="ts">
	import type { ContactFormLabels } from '$lib/content';

	interface Props {
		labels: ContactFormLabels;
	}

	let { labels }: Props = $props();

	// Form state
	let formData = $state({
		name: '',
		email: '',
		company: '',
		message: ''
	});

	let isSubmitting = $state(false);
	let submitStatus = $state<'idle' | 'success' | 'error'>('idle');

	// Form validation
	function validateForm(): boolean {
		return !!(
			formData.name.trim() &&
			formData.email.trim() &&
			formData.email.includes('@') &&
			formData.message.trim()
		);
	}

	// Handle form submission
	async function handleSubmit(event: Event) {
		event.preventDefault();

		if (!validateForm()) return;

		isSubmitting = true;
		submitStatus = 'idle';

		try {
			// Simulate API call - replace with actual endpoint
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// For now, just log the form data
			console.log('Form submitted:', formData);

			submitStatus = 'success';

			// Reset form after successful submission
			formData = {
				name: '',
				email: '',
				company: '',
				message: ''
			};
		} catch (error) {
			console.error('Form submission error:', error);
			submitStatus = 'error';
		} finally {
			isSubmitting = false;
		}
	}
</script>

<form onsubmit={handleSubmit} class="mx-auto max-w-lg space-y-6">
	<!-- Name Field -->
	<div>
		<label for="name" class="text-heading mb-2 block text-sm font-medium">
			{labels.name} <span class="text-red-500">*</span>
		</label>
		<input
			type="text"
			id="name"
			bind:value={formData.name}
			placeholder={labels.namePlaceholder}
			required
			class="border-subtle focus:ring-primary focus:border-primary text-heading w-full rounded-lg border bg-white px-4 py-3 placeholder-gray-400 transition-colors focus:ring-2"
		/>
	</div>

	<!-- Email Field -->
	<div>
		<label for="email" class="text-heading mb-2 block text-sm font-medium">
			{labels.email} <span class="text-red-500">*</span>
		</label>
		<input
			type="email"
			id="email"
			bind:value={formData.email}
			placeholder={labels.emailPlaceholder}
			required
			class="border-subtle focus:ring-primary focus:border-primary text-heading w-full rounded-lg border bg-white px-4 py-3 placeholder-gray-400 transition-colors focus:ring-2"
		/>
	</div>

	<!-- Company Field -->
	<div>
		<label for="company" class="text-heading mb-2 block text-sm font-medium">
			{labels.company}
		</label>
		<input
			type="text"
			id="company"
			bind:value={formData.company}
			placeholder={labels.companyPlaceholder}
			class="border-subtle focus:ring-primary focus:border-primary text-heading w-full rounded-lg border bg-white px-4 py-3 placeholder-gray-400 transition-colors focus:ring-2"
		/>
	</div>

	<!-- Message Field -->
	<div>
		<label for="message" class="text-heading mb-2 block text-sm font-medium">
			{labels.message} <span class="text-red-500">*</span>
		</label>
		<textarea
			id="message"
			bind:value={formData.message}
			placeholder={labels.messagePlaceholder}
			required
			rows="5"
			class="border-subtle focus:ring-primary focus:border-primary text-heading resize-vertical w-full rounded-lg border bg-white px-4 py-3 placeholder-gray-400 transition-colors focus:ring-2"
		></textarea>
	</div>

	<!-- Submit Button -->
	<button
		type="submit"
		disabled={!validateForm() || isSubmitting}
		class="bg-primary hover:bg-primary-hover flex w-full items-center justify-center gap-2 rounded-lg px-8 py-4 font-bold text-white transition-colors duration-300 disabled:cursor-not-allowed disabled:bg-gray-300"
	>
		{#if isSubmitting}
			<svg class="h-5 w-5 animate-spin" viewBox="0 0 24 24">
				<circle
					class="opacity-25"
					cx="12"
					cy="12"
					r="10"
					stroke="currentColor"
					stroke-width="4"
					fill="none"
				></circle>
				<path
					class="opacity-75"
					fill="currentColor"
					d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
				></path>
			</svg>
			Sending...
		{:else}
			{labels.submit}
		{/if}
	</button>

	<!-- Status Messages -->
	{#if submitStatus === 'success'}
		<div class="rounded-lg border border-green-200 bg-green-50 p-4">
			<p class="text-sm font-medium text-green-800">{labels.successMessage}</p>
		</div>
	{/if}

	{#if submitStatus === 'error'}
		<div class="rounded-lg border border-red-200 bg-red-50 p-4">
			<p class="text-sm font-medium text-red-800">{labels.errorMessage}</p>
		</div>
	{/if}
</form>
