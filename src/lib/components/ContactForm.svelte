<script lang="ts">
	import type { ContactFormLabels } from '$lib/content';

	interface Props {
		labels: ContactFormLabels;
	}

	let { labels }: Props = $props();

	// Form state
	let formData = $state({
		name: '',
		email: '',
		company: '',
		message: ''
	});

	let isSubmitting = $state(false);
	let submitStatus = $state<'idle' | 'success' | 'error'>('idle');

	// Form validation
	function validateForm(): boolean {
		return !!(
			formData.name.trim() &&
			formData.email.trim() &&
			formData.email.includes('@') &&
			formData.message.trim()
		);
	}

	// Handle form submission
	async function handleSubmit(event: Event) {
		event.preventDefault();

		if (!validateForm()) return;

		isSubmitting = true;
		submitStatus = 'idle';

		try {
			// Simulate API call - replace with actual endpoint
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// For now, just log the form data
			console.log('Form submitted:', formData);

			submitStatus = 'success';

			// Reset form after successful submission
			formData = {
				name: '',
				email: '',
				company: '',
				message: ''
			};
		} catch (error) {
			console.error('Form submission error:', error);
			submitStatus = 'error';
		} finally {
			isSubmitting = false;
		}
	}
</script>

<form onsubmit={handleSubmit} class="mx-auto max-w-lg space-y-6">
	<!-- Name Field -->
	<div>
		<label for="name" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.name} <span class="text-red-400">*</span>
		</label>
		<input
			type="text"
			id="name"
			bind:value={formData.name}
			placeholder={labels.namePlaceholder}
			required
			class="w-full rounded-lg bg-white/10 backdrop-blur-md border border-white/20 px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/30 focus:outline-none"
		/>
	</div>

	<!-- Email Field -->
	<div>
		<label for="email" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.email} <span class="text-red-400">*</span>
		</label>
		<input
			type="email"
			id="email"
			bind:value={formData.email}
			placeholder={labels.emailPlaceholder}
			required
			class="w-full rounded-lg bg-white/10 backdrop-blur-md border border-white/20 px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/30 focus:outline-none"
		/>
	</div>

	<!-- Company Field -->
	<div>
		<label for="company" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.company}
		</label>
		<input
			type="text"
			id="company"
			bind:value={formData.company}
			placeholder={labels.companyPlaceholder}
			class="w-full rounded-lg bg-white/10 backdrop-blur-md border border-white/20 px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/30 focus:outline-none"
		/>
	</div>

	<!-- Message Field -->
	<div>
		<label for="message" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.message} <span class="text-red-400">*</span>
		</label>
		<textarea
			id="message"
			bind:value={formData.message}
			placeholder={labels.messagePlaceholder}
			required
			rows="5"
			class="w-full resize-vertical rounded-lg bg-white/10 backdrop-blur-md border border-white/20 px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/30 focus:outline-none"
		></textarea>
	</div>

	<!-- Submit Button -->
	<button
		type="submit"
		disabled={!validateForm() || isSubmitting}
		class="flex w-full items-center justify-center gap-2 rounded-lg bg-white/20 backdrop-blur-md border border-white/30 px-8 py-4 font-bold text-white transition-all duration-300 hover:bg-white/30 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:cursor-not-allowed disabled:bg-white/10 disabled:text-white/50 disabled:hover:scale-100"
	>
		{#if isSubmitting}
			<svg class="h-5 w-5 animate-spin" viewBox="0 0 24 24">
				<circle
					class="opacity-25"
					cx="12"
					cy="12"
					r="10"
					stroke="currentColor"
					stroke-width="4"
					fill="none"
				></circle>
				<path
					class="opacity-75"
					fill="currentColor"
					d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
				></path>
			</svg>
			Sending...
		{:else}
			{labels.submit}
		{/if}
	</button>

	<!-- Status Messages -->
	{#if submitStatus === 'success'}
		<div class="rounded-lg bg-green-500/20 backdrop-blur-md border border-green-400/30 p-4 shadow-lg">
			<p class="text-sm font-medium text-green-100 drop-shadow-md">{labels.successMessage}</p>
		</div>
	{/if}

	{#if submitStatus === 'error'}
		<div class="rounded-lg bg-red-500/20 backdrop-blur-md border border-red-400/30 p-4 shadow-lg">
			<p class="text-sm font-medium text-red-100 drop-shadow-md">{labels.errorMessage}</p>
		</div>
	{/if}
</form>
