<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { writable } from 'svelte/store';
	import Logo from '$lib/Logo.svelte';
	import { getCurrentContent } from '$lib/content';
	import NavigationDots from '$lib/components/NavigationDots.svelte';
	import ContactForm from '$lib/components/ContactForm.svelte';

	// Get current content
	const content = getCurrentContent();

	// GSAP will be imported dynamically in onMount for better performance
	let gsap: any;
	let ScrollToPlugin: any;
	let Observer: any;

	// Full-page scroll state
	const sectionIds = ['hero', 'philosophy', 'services', 'contact'];
	const sectionNames = content.navigation.sectionNames;
	let currentSectionIndex = 0;
	let isAnimating = false;
	const activeSection = writable(sectionIds[0]);

	// Reactive state for Services tabs (Svelte 5 syntax)
	let activeTab = $state('traditional');

	// Store event handler references for cleanup
	let keydownHandler: ((e: KeyboardEvent) => void) | null = null;
	let scrollHandler: (() => void) | null = null;

	// Function to detect current section based on scroll position
	function updateCurrentSection() {
		if (isAnimating) return;

		const windowHeight = window.innerHeight;

		// Find which section is currently most visible
		let newSectionIndex = 0;
		let maxVisibility = 0;

		sectionIds.forEach((sectionId, index) => {
			const element = document.getElementById(sectionId);
			if (!element) return;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top;
			const elementBottom = rect.bottom;

			// Calculate how much of the section is visible
			const visibleTop = Math.max(0, -elementTop);
			const visibleBottom = Math.min(
				windowHeight,
				windowHeight - Math.max(0, elementBottom - windowHeight)
			);
			const visibleHeight = Math.max(0, visibleBottom - visibleTop);
			const visibility = visibleHeight / windowHeight;

			if (visibility > maxVisibility) {
				maxVisibility = visibility;
				newSectionIndex = index;
			}
		});

		if (newSectionIndex !== currentSectionIndex) {
			currentSectionIndex = newSectionIndex;
			activeSection.set(sectionIds[newSectionIndex]);
		}
	}

	function scrollToSection(id: string) {
		const index = sectionIds.indexOf(id);
		if (index === -1 || isAnimating) return;

		// Update current section index and active section
		currentSectionIndex = index;
		isAnimating = true;
		activeSection.set(id);

		// Add scrolling class to prevent manual scrolling during animation
		document.body.classList.add('scrolling');

		// Reset all animations first
		resetAllAnimations();

		gsap.to(window, {
			scrollTo: { y: `#${id}`, autoKill: false },
			duration: 1.2,
			ease: 'power3.inOut',
			onComplete: () => {
				isAnimating = false;
				// Remove scrolling class
				document.body.classList.remove('scrolling');
				// Trigger content animation for the new section
				playContentAnimation(id);
			}
		});
	}

	onMount(async () => {
		// Dynamic import of GSAP for better performance
		const gsapModule = await import('gsap');
		const scrollToPluginModule = await import('gsap/ScrollToPlugin');
		const observerModule = await import('gsap/Observer');

		gsap = gsapModule.gsap;
		ScrollToPlugin = scrollToPluginModule.ScrollToPlugin;
		Observer = observerModule.Observer;

		gsap.registerPlugin(ScrollToPlugin, Observer);

		// Ensure page always starts at hero section on load/refresh
		gsap.set(window, { scrollTo: { y: '#hero', autoKill: false } });

		// Reset current section state
		currentSectionIndex = 0;
		activeSection.set('hero');

		// Initialize all section animations in reset state
		resetAllAnimations();

		// Play initial hero animation
		playHeroAnimation();

		// Set up Observer for full-page scroll
		Observer.create({
			target: window,
			type: 'wheel,touch',
			wheelSpeed: -1,
			onUp: () => {
				if (currentSectionIndex < sectionIds.length - 1 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex + 1]);
				}
			},
			onDown: () => {
				if (currentSectionIndex > 0 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex - 1]);
				}
			},
			tolerance: 50, // Increased tolerance for better control
			preventDefault: true
		});

		// Add keyboard navigation
		keydownHandler = (e: KeyboardEvent) => {
			if (isAnimating) return;

			switch (e.key) {
				case 'ArrowUp':
				case 'PageUp':
					e.preventDefault();
					if (currentSectionIndex > 0) {
						scrollToSection(sectionIds[currentSectionIndex - 1]);
					}
					break;
				case 'ArrowDown':
				case 'PageDown':
				case ' ': // Spacebar
					e.preventDefault();
					if (currentSectionIndex < sectionIds.length - 1) {
						scrollToSection(sectionIds[currentSectionIndex + 1]);
					}
					break;
				case 'Home':
					e.preventDefault();
					scrollToSection(sectionIds[0]);
					break;
				case 'End':
					e.preventDefault();
					scrollToSection(sectionIds[sectionIds.length - 1]);
					break;
			}
		};

		window.addEventListener('keydown', keydownHandler);

		// Add scroll listener to update current section
		scrollHandler = () => {
			updateCurrentSection();
		};

		window.addEventListener('scroll', scrollHandler, { passive: true });

		// Initial section detection
		setTimeout(updateCurrentSection, 100);
	});

	function playContentAnimation(sectionId: string) {
		switch (sectionId) {
			case 'hero':
				playHeroAnimation();
				break;
			case 'philosophy':
				playPhilosophyAnimation();
				break;
			case 'services':
				playServicesAnimation();
				break;
			case 'contact':
				playContactAnimation();
				break;
		}
	}

	function resetAllAnimations() {
		if (!gsap) return;

		// Reset all sections to initial state
		gsap.set('.hero-logo, .hero-headline, .hero-subheadline', { opacity: 0, y: 30 });
		gsap.set('.hero-cta', { opacity: 0, y: 30, scale: 0.8 });
		gsap.set('.philosophy-heading, .philosophy-paragraph, .philosophy-visual', {
			opacity: 0,
			y: 30
		});
		gsap.set('.services-header, .services-tabs, .service-item', { opacity: 0, y: 30 });
		gsap.set('.cta-heading, .contact-form', { opacity: 0, y: 30 });
	}

	onDestroy(() => {
		// Clean up GSAP Observer instances
		if (Observer) {
			Observer.getAll().forEach((observer: any) => observer.kill());
		}

		// Remove event listeners
		if (keydownHandler) {
			window.removeEventListener('keydown', keydownHandler);
		}
		if (scrollHandler) {
			window.removeEventListener('scroll', scrollHandler);
		}
	});

	function switchTab(tab: 'traditional' | 'tech') {
		if (activeTab === tab || !gsap) return;

		// Animate out current content
		gsap.to('.tab-content', {
			duration: 0.3,
			opacity: 0,
			y: 20,
			ease: 'power2.out',
			onComplete: () => {
				activeTab = tab;
				// Animate in new content
				gsap.fromTo(
					'.tab-content',
					{ opacity: 0, y: 20 },
					{ duration: 0.4, opacity: 1, y: 0, ease: 'power2.out' }
				);
			}
		});
	}

	function playHeroAnimation() {
		if (!gsap) return;

		// Hero section load-in sequence
		const tl = gsap.timeline();

		tl.to('.hero-logo', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
			.to(
				'.hero-headline',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.4'
			)
			.to(
				'.hero-subheadline',
				{
					duration: 0.8,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.6'
			)
			.to(
				'.hero-cta',
				{
					duration: 0.8,
					y: 0,
					opacity: 1,
					scale: 1,
					ease: 'back.out(1.7)'
				},
				'-=0.3'
			);
	}

	function playPhilosophyAnimation() {
		if (!gsap) return;

		// Philosophy section animation sequence
		const tl = gsap.timeline();

		tl.to('.philosophy-heading', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
			.to(
				'.philosophy-paragraph',
				{
					duration: 0.8,
					y: 0,
					opacity: 1,
					stagger: 0.2,
					ease: 'power2.out'
				},
				'-=0.6'
			)
			.to(
				'.philosophy-visual',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					scale: 1,
					ease: 'power2.out'
				},
				'-=0.8'
			);
	}

	function playServicesAnimation() {
		if (!gsap) return;

		// Services section animation sequence
		const tl = gsap.timeline();

		tl.to('.services-header', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
			.to(
				'.services-tabs',
				{
					duration: 0.8,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.6'
			)
			.to(
				'.service-item',
				{
					duration: 0.6,
					y: 0,
					opacity: 1,
					stagger: 0.1,
					ease: 'power2.out'
				},
				'-=0.4'
			);
	}

	function playContactAnimation() {
		if (!gsap) return;

		// Contact section animation sequence
		const tl = gsap.timeline();

		tl.to('.cta-heading', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}).to(
			'.contact-form',
			{
				duration: 0.8,
				y: 0,
				opacity: 1,
				ease: 'power2.out'
			},
			'-=0.6'
		);
	}
</script>

<!-- Navigation Dots -->
<NavigationDots sections={sectionIds} {sectionNames} {activeSection} {scrollToSection} />

<video
	class="fixed top-0 left-0 z-0 h-full w-full object-cover"
	src="/background.mp4"
	muted
	loop
	autoplay
	playsinline
></video>

<!-- Hero Section -->
<section
	id="hero"
	class="relative flex h-screen w-full flex-col items-center justify-center bg-transparent p-8 text-center"
>
	<div class="hero-logo">
		<Logo class="text-primary mx-auto mb-8 h-16 w-16" />
	</div>

	<h1
		class="hero-headline font-display text-heading text-5xl font-bold tracking-tighter lg:text-7xl"
	>
		{content.hero.headline}
	</h1>

	<p class="hero-subheadline text-content mx-auto mt-6 max-w-2xl text-lg lg:text-xl">
		{content.hero.subHeadline}
	</p>

	<button
		onclick={() => scrollToSection('contact')}
		class="hero-cta bg-primary hover:bg-primary-hover mt-10 inline-block rounded-lg px-10 py-4 font-bold text-white transition-colors duration-300"
	>
		{content.hero.ctaText}
	</button>
</section>

<!-- Philosophy Section -->
<section
	id="philosophy"
	class="relative flex h-screen w-full items-center justify-center bg-transparent"
>
	<div class="container mx-auto grid grid-cols-1 items-center gap-16 px-8 lg:grid-cols-2">
		<!-- Left Column: Text Content -->
		<div>
			<h2
				class="philosophy-heading font-display text-heading text-4xl font-bold tracking-tighter lg:text-5xl"
			>
				{content.philosophy.heading}
			</h2>

			{#each content.philosophy.paragraphs as paragraph}
				<p class="philosophy-paragraph text-content mt-6 text-lg">
					{paragraph}
				</p>
			{/each}
		</div>

		<!-- Right Column: Visual Element -->
		<div class="philosophy-visual flex items-center justify-center">
			<div class="relative h-80 w-80">
				<!-- Abstract animated visual -->
				<div
					class="absolute inset-0 animate-pulse rounded-full bg-gradient-to-br from-violet-500 to-violet-600 opacity-20"
				></div>
				<div
					class="absolute inset-4 animate-pulse rounded-full bg-gradient-to-tl from-violet-600 to-violet-500 opacity-30"
					style="animation-delay: 0.5s;"
				></div>
				<div
					class="absolute inset-8 animate-pulse rounded-full bg-violet-500 opacity-40"
					style="animation-delay: 1s;"
				></div>
			</div>
		</div>
	</div>
</section>

<!-- Services Section -->
<section
	id="services"
	class="relative flex h-screen w-full items-center justify-center bg-slate-50"
>
	<div class="container mx-auto max-h-screen overflow-hidden px-8">
		<!-- Header Area -->
		<div class="services-header mx-auto mb-8 max-w-3xl text-center">
			<h2 class="text-heading text-4xl font-bold tracking-tight">
				{content.services.heading}
			</h2>
			<p class="text-content mt-4 text-lg">
				{content.services.subHeading}
			</p>
		</div>

		<!-- Tab Navigation -->
		<div class="services-tabs border-subtle mb-8 flex justify-center border-b">
			<button
				onclick={() => switchTab('traditional')}
				class="-mb-px border-b-2 px-6 py-3 font-bold transition-colors duration-300 {activeTab ===
				'traditional'
					? 'border-primary text-primary'
					: 'text-content hover:text-primary border-transparent'}"
			>
				{content.services.tabs.traditional.label}
			</button>
			<button
				onclick={() => switchTab('tech')}
				class="-mb-px border-b-2 px-6 py-3 font-bold transition-colors duration-300 {activeTab ===
				'tech'
					? 'border-primary text-primary'
					: 'text-content hover:text-primary border-transparent'}"
			>
				{content.services.tabs.tech.label}
			</button>
		</div>

		<!-- Tab Content Panels -->
		<div class="tab-content">
			{#if activeTab === 'traditional'}
				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
					{#each content.services.tabs.traditional.services as service}
						<div
							class="service-item border-subtle rounded-lg border bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md"
						>
							<h3 class="text-heading mb-3 text-lg font-bold">
								{service.title}
							</h3>
							<p class="text-content">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}

			{#if activeTab === 'tech'}
				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
					{#each content.services.tabs.tech.services as service}
						<div
							class="service-item border-subtle rounded-lg border bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md"
						>
							<h3 class="text-heading mb-3 text-lg font-bold">
								{service.title}
							</h3>
							<p class="text-content">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</section>

<!-- Contact Section -->
<section
	id="contact"
	class="relative flex h-screen w-full items-center justify-center bg-transparent"
>
	<div class="container mx-auto max-h-screen overflow-y-hidden px-8">
		<div class="mb-12 text-center">
			<h2
				class="cta-heading font-display text-heading text-4xl font-bold tracking-tighter lg:text-5xl"
			>
				{content.contact.heading}
			</h2>
			<p class="text-content mx-auto mt-6 max-w-2xl text-lg">
				{content.contact.subHeading}
			</p>
		</div>

		<div class="contact-form">
			<ContactForm labels={content.contact.form} />
		</div>
	</div>
</section>
