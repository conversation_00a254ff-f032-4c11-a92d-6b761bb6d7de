@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap');
@import 'tailwindcss';

@theme {
  --color-primary: #7C3AED;
  --color-primary-hover: #6D28D9;
  --color-heading: #0F172A;
  --color-text: #1E293B;
  --color-background: #F8FAFC;
  --color-subtle: #E2E8F0;
  --color-subtle-hover: #CBD5E1;

  --font-sans: "Inter", sans-serif;
  --font-display: "Space Grotesk", "Inter", sans-serif;
}

/* Utility classes for custom colors */
.bg-primary { background-color: var(--color-primary); }
.bg-primary-hover { background-color: var(--color-primary-hover); }
.bg-background { background-color: var(--color-background); }
.text-primary { color: var(--color-primary); }
.text-heading { color: var(--color-heading); }
.text-content { color: var(--color-text); }
.border-subtle { border-color: var(--color-subtle); }
.border-primary { border-color: var(--color-primary); }

/* Font utility classes */
.font-display { font-family: var(--font-display); }
.font-sans { font-family: var(--font-sans); }

/* Hover states */
.hover\:bg-primary-hover:hover { background-color: var(--color-primary-hover); }
.hover\:text-primary:hover { color: var(--color-primary); }

/* Custom styles for full-page scroll experience */
html, body {
  height: 100%;
  font-family: var(--font-sans);
  background-color: var(--color-background);
  color: var(--color-text);
  scroll-behavior: auto; /* Let GSAP handle smooth scrolling */
}

/* Hide scrollbar while keeping scroll functionality */
html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

html::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none; /* Chrome, Safari, Opera */
}

/* Prevent default scrolling during animations */
body.scrolling {
  overflow: hidden;
}

/* Custom animation classes for GSAP */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in {
  opacity: 0;
}

.stagger-item {
  opacity: 0;
  transform: translateY(20px);
}

/* Form styling enhancements */
input:focus, textarea:focus {
  outline: none;
}

/* Smooth transitions for form elements */
input, textarea, button {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for contact section if needed */
.contact-section {
  scrollbar-width: thin;
  scrollbar-color: var(--color-subtle) transparent;
}

.contact-section::-webkit-scrollbar {
  width: 6px;
}

.contact-section::-webkit-scrollbar-track {
  background: transparent;
}

.contact-section::-webkit-scrollbar-thumb {
  background-color: var(--color-subtle);
  border-radius: 3px;
}
